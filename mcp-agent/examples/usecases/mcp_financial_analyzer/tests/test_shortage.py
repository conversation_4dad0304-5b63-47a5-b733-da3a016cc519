from agents.shortage_analyzer_agent import create_shortage_analyzer_agent
from schemas.agent_schemas import ShortageAnalysisInputSchema
from asyncio import run

agent = create_shortage_analyzer_agent("Your Company")

input_data = ShortageAnalysisInputSchema(
    company_name="TechCorp",
    financial_data="CPU available 150, required 200, weight 0.3",
    message="Analyze Q4 supply chain risks"
)

result = run(agent.enhanced_shortage_analysis(input_data))
print(f"Risk Level: {result.risk_level}")
print(f"Shortage Index: {result.shortage_index:.3f}")